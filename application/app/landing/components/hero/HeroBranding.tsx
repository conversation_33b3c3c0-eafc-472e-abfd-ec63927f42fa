/** @format */

'use client';

import { colors } from '@/app/colors';
import {
	LANDING_PAGE_DATA,
	getPOICategories,
	getPOISubcategories,
} from '@/app/shared/poi/constants';
import React, { useEffect, useState } from 'react';

interface HeroBrandingProps {
	onGetStarted: () => void;
}

const HeroBranding: React.FC<HeroBrandingProps> = ({ onGetStarted }) => {
	// Get real data counts
	const categoriesCount = getPOICategories().length;
	const subcategoriesCount = getPOISubcategories().length;
	const [isHovered, setIsHovered] = useState(false);
	const [screenSize, setScreenSize] = useState<'mobile' | 'tablet' | 'desktop'>(
		'desktop'
	);

	// Detect screen size for responsive behavior
	useEffect(() => {
		const updateScreenSize = () => {
			const width = window.innerWidth;
			if (width < 768) {
				setScreenSize('mobile');
			} else if (width < 1024) {
				setScreenSize('tablet');
			} else {
				setScreenSize('desktop');
			}
		};

		updateScreenSize();
		window.addEventListener('resize', updateScreenSize);
		return () => window.removeEventListener('resize', updateScreenSize);
	}, []);

	return (
		<div className='relative w-full h-full'>
			{/* Profound Side-Based Hero Layout */}
			<div
				className='relative overflow-hidden transition-all duration-700 h-full'
				onMouseEnter={() => setIsHovered(true)}
				onMouseLeave={() => setIsHovered(false)}>
				{/* Dynamic Background Elements */}
				<div className='absolute inset-0 opacity-15'>
					{/* Large geometric shapes positioned asymmetrically */}
					<div
						className='absolute top-1/4 left-0 w-32 h-32 rounded-full'
						style={{
							background: `radial-gradient(circle, ${colors.brand.blue}40 0%, transparent 70%)`,
							transform: isHovered ? 'scale(1.3) translateX(20px)' : 'scale(1)',
							transition: 'transform 1s ease-out',
						}}
					/>
					<div
						className='absolute top-1/2 right-0 w-24 h-24 rounded-lg'
						style={{
							background: `linear-gradient(45deg, ${colors.brand.green}30 0%, transparent 70%)`,
							transform: isHovered
								? 'scale(1.4) rotate(180deg) translateX(-30px)'
								: 'scale(1) rotate(45deg)',
							transition: 'transform 1s ease-out',
						}}
					/>
					<div
						className='absolute bottom-1/4 left-1/3 w-20 h-20 rounded-full'
						style={{
							background: `conic-gradient(from 0deg, ${colors.brand.navy}50, transparent, ${colors.brand.blue}30)`,
							transform: isHovered
								? 'scale(1.5) translateY(-20px)'
								: 'scale(1)',
							transition: 'transform 1s ease-out',
						}}
					/>
				</div>

				{/* Main Content - Side Layout */}
				<div className='relative z-10 h-full flex items-center'>
					{/* Two-Column Layout */}
					<div className='w-full grid grid-cols-1 lg:grid-cols-12 gap-8 lg:gap-12 px-6 lg:px-12'>
						{/* Left Column - Brand Identity */}
						<div className='lg:col-span-7 space-y-8 lg:space-y-12'>
							{/* Logo Section - Left Aligned */}
							<div className='flex justify-start lg:justify-start mb-8'>
								<div
									className='relative group'
									style={{
										transform: isHovered
											? 'translateY(-10px) scale(1.08)'
											: 'translateY(0) scale(1)',
										transition: 'transform 0.6s ease-out',
									}}>
									<img
										src='/logo/512x512.png'
										alt='Wizlop Logo'
										className='w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 object-contain'
										style={{
											filter: `brightness(0) saturate(100%) invert(27%) sepia(96%) saturate(2555%) hue-rotate(202deg) brightness(101%) contrast(101%)`,
										}}
									/>
									{/* Enhanced glowing ring effect */}
									<div
										className='absolute inset-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700'
										style={{
											background: `conic-gradient(from 0deg, ${colors.brand.blue}50, ${colors.brand.green}50, ${colors.brand.navy}50, ${colors.brand.blue}50)`,
											filter: 'blur(12px)',
											transform: 'scale(1.4)',
										}}
									/>
								</div>
							</div>

							{/* Brand Name - Left Aligned */}
							<div className='space-y-6 text-left'>
								<h1
									className='font-black tracking-tight leading-none'
									style={{
										fontSize:
											screenSize === 'mobile'
												? '2.5rem'
												: screenSize === 'tablet'
												? '3.5rem'
												: '4.5rem',
									}}>
									<span
										className='bg-clip-text text-transparent block'
										style={{
											backgroundImage: `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.brand.blue} 50%, ${colors.brand.green} 100%)`,
											WebkitBackgroundClip: 'text',
											WebkitTextFillColor: 'transparent',
											transform: isHovered ? 'scale(1.03)' : 'scale(1)',
											transition: 'transform 0.5s ease-out',
										}}>
										{LANDING_PAGE_DATA.branding.companyName}
									</span>
								</h1>

								{/* Tagline with side accent */}
								<div className='relative'>
									<p
										className='font-semibold tracking-wide text-base sm:text-lg lg:text-xl'
										style={{
											color: colors.brand.blue,
											transform: isHovered
												? 'translateX(8px)'
												: 'translateX(0)',
											transition: 'transform 0.5s ease-out',
										}}>
										{LANDING_PAGE_DATA.branding.tagline}
									</p>
									{/* Side accent line */}
									<div
										className='absolute left-0 top-0 bottom-0 rounded-full transition-all duration-700'
										style={{
											background: `linear-gradient(180deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
											width: isHovered ? '4px' : '2px',
											transform: 'translateX(-12px)',
										}}
									/>
								</div>
							</div>
						</div>

						{/* Right Column - Description & CTA */}
						<div className='lg:col-span-5 space-y-8 lg:space-y-10 flex flex-col justify-center'>
							{/* Description with floating effect */}
							<div className='relative'>
								<div
									className='p-6 lg:p-8 rounded-2xl border backdrop-blur-sm'
									style={{
										background: `linear-gradient(135deg, ${colors.neutral.cloudWhite}95 0%, ${colors.ui.blue50}30 100%)`,
										borderColor: colors.ui.gray200,
										boxShadow: '0 8px 32px rgba(51, 194, 255, 0.08)',
										transform: isHovered
											? 'translateY(-8px) scale(1.02)'
											: 'translateY(0) scale(1)',
										transition: 'transform 0.6s ease-out',
									}}>
									<p
										className='text-base sm:text-lg lg:text-xl leading-relaxed font-medium'
										style={{
											color: colors.neutral.slateGray,
										}}>
										{LANDING_PAGE_DATA.branding.heroDescription}
									</p>
								</div>

								{/* Floating accent */}
								<div
									className='absolute -top-2 -right-2 w-4 h-4 rounded-full'
									style={{
										background: `linear-gradient(45deg, ${colors.brand.green} 0%, ${colors.brand.blue} 100%)`,
										transform: isHovered
											? 'scale(1.5) rotate(180deg)'
											: 'scale(1)',
										transition: 'transform 0.8s ease-out',
									}}
								/>
							</div>

							{/* Enhanced CTA Section */}
							<div className='space-y-6'>
								<button
									onClick={onGetStarted}
									className='group relative w-full lg:w-auto px-8 py-4 sm:px-12 sm:py-5 rounded-2xl font-bold text-lg sm:text-xl transition-all duration-500 transform hover:scale-105 active:scale-95'
									style={{
										background: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
										color: 'white',
										boxShadow: isHovered
											? `0 20px 40px rgba(51, 194, 255, 0.4)`
											: '0 10px 25px rgba(51, 194, 255, 0.2)',
									}}>
									<span className='relative z-10'>Start Exploring</span>
									{/* Enhanced animated background */}
									<div
										className='absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500'
										style={{
											background: `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.brand.blue} 100%)`,
										}}
									/>
									{/* Enhanced ripple effect */}
									<div
										className='absolute inset-0 rounded-2xl opacity-0 group-active:opacity-100 transition-opacity duration-200'
										style={{
											background: `radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%)`,
										}}
									/>
								</button>

								{/* Enhanced stats with side layout */}
								<div className='flex items-center justify-start lg:justify-center space-x-6 text-sm opacity-75'>
									<div className='text-left lg:text-center'>
										<div
											className='font-bold text-lg'
											style={{ color: colors.brand.blue }}>
											{categoriesCount}
										</div>
										<div style={{ color: colors.neutral.slateGray }}>
											Categories
										</div>
									</div>
									<div
										className='w-px h-8'
										style={{ background: colors.ui.gray200 }}
									/>
									<div className='text-left lg:text-center'>
										<div
											className='font-bold text-lg'
											style={{ color: colors.brand.green }}>
											{subcategoriesCount}
										</div>
										<div style={{ color: colors.neutral.slateGray }}>
											Locations
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default HeroBranding;
