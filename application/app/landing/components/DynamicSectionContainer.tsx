/** @format */

'use client';

import React from 'react';
import { getDynamicSectionProps } from '../utils/dynamicLayout';

interface DynamicSectionContainerProps {
	sectionType: 'hero' | 'section1' | 'section2' | 'footer';
	children: React.ReactNode;
	className?: string;
	style?: React.CSSProperties;
}

const DynamicSectionContainer: React.FC<DynamicSectionContainerProps> = ({
	sectionType,
	children,
	className = '',
	style = {},
}) => {
	const props = getDynamicSectionProps(sectionType, className, style);

	return <div {...props}>{children}</div>;
};

export default DynamicSectionContainer;
