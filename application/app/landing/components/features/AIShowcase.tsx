/** @format */

'use client';

import { colors } from '@/app/colors';
import { useViewportHeight } from '@/app/landing/utils/responsiveUtils';
import { LANDING_PAGE_DATA } from '@/app/shared/poi/constants';
import React, { useEffect, useState } from 'react';
import {
	FiArrowRight,
	FiMapPin,
	FiMessageCircle,
	FiSearch,
	FiStar,
	FiTarget,
	FiZap,
} from 'react-icons/fi';

interface AIShowcaseProps {
	onGetStarted: () => void;
}

interface DemoStep {
	icon: React.ReactNode;
	title: string;
	description: string;
	isActive: boolean;
}

const AIShowcase: React.FC<AIShowcaseProps> = ({ onGetStarted }) => {
	// Use viewport height for responsive section sizing
	const { screenSize, minHeight, padding } = useViewportHeight('section');

	const [currentScenarioIndex, setCurrentScenarioIndex] = useState(0);
	const [currentStep, setCurrentStep] = useState(0);
	const [userText, setUserText] = useState('');
	const [aiText, setAIText] = useState('');
	const [isTyping, setIsTyping] = useState(false);
	const [showUserMessage, setShowUserMessage] = useState(false);
	const [showAIResponse, setShowAIResponse] = useState(false);
	const [showResults, setShowResults] = useState(false);

	const scenarios = LANDING_PAGE_DATA.aiDemoScenarios;
	const currentScenario = scenarios[currentScenarioIndex];

	// Animation cycle
	useEffect(() => {
		const animationCycle = async () => {
			// Reset states
			setShowUserMessage(false);
			setShowAIResponse(false);
			setShowResults(false);
			setUserText('');
			setAIText('');
			setIsTyping(false);
			setCurrentStep(0);

			// Wait before starting
			await new Promise((resolve) => setTimeout(resolve, 1000));

			// Type user message
			setShowUserMessage(true);
			for (let i = 0; i <= currentScenario.userQuery.length; i++) {
				setUserText(currentScenario.userQuery.slice(0, i));
				await new Promise((resolve) => setTimeout(resolve, 50));
			}

			// Wait and show processing
			await new Promise((resolve) => setTimeout(resolve, 800));
			setIsTyping(true);

			// Cycle through processing steps
			for (
				let step = 0;
				step < currentScenario.processingSteps.length;
				step++
			) {
				setCurrentStep(step);
				await new Promise((resolve) => setTimeout(resolve, 1000));
			}

			// Type AI response
			setIsTyping(false);
			setShowAIResponse(true);
			for (let i = 0; i <= currentScenario.aiResponse.length; i++) {
				setAIText(currentScenario.aiResponse.slice(0, i));
				await new Promise((resolve) => setTimeout(resolve, 30));
			}

			// Show results
			await new Promise((resolve) => setTimeout(resolve, 800));
			setShowResults(true);

			// Wait before next cycle
			await new Promise((resolve) => setTimeout(resolve, 4000));

			// Move to next scenario
			setCurrentScenarioIndex((prev) => (prev + 1) % scenarios.length);
		};

		animationCycle();
	}, [
		currentScenarioIndex,
		currentScenario.userQuery,
		currentScenario.aiResponse,
		currentScenario.processingSteps.length,
	]);

	const processingSteps: DemoStep[] = [
		{
			icon: <FiTarget className='w-4 h-4' />,
			title: 'Understanding Context',
			description: 'AI analyzes your natural language query',
			isActive: currentStep >= 0 && isTyping,
		},
		{
			icon: <FiMapPin className='w-4 h-4' />,
			title: 'Location Processing',
			description: 'Identifying geographic preferences',
			isActive: currentStep >= 1 && isTyping,
		},
		{
			icon: <FiSearch className='w-4 h-4' />,
			title: 'Smart Filtering',
			description: 'Applying intelligent filters and ranking',
			isActive: currentStep >= 2 && isTyping,
		},
		{
			icon: <FiZap className='w-4 h-4' />,
			title: 'Personalized Results',
			description: 'Delivering tailored recommendations',
			isActive: currentStep >= 3 && isTyping,
		},
	];

	return (
		<div
			className='bg-transparent viewport-section'
			style={{
				minHeight,
				padding: `${padding} 0`,
			}}>
			{/* Split-Screen Container Layout */}
			<div className='w-full px-4 sm:px-6 lg:px-8'>
				{/* Profound Header Design */}
				<div className='relative mb-16 lg:mb-20'>
					{/* Dynamic background elements */}
					<div className='absolute inset-0 opacity-8'>
						<div
							className='absolute top-0 right-0 w-40 h-40 rounded-full'
							style={{
								background: `radial-gradient(circle, ${colors.brand.green}30 0%, transparent 70%)`,
							}}
						/>
						<div
							className='absolute bottom-0 left-1/3 w-24 h-24 rounded-lg rotate-12'
							style={{
								background: `linear-gradient(45deg, ${colors.brand.blue}25 0%, transparent 70%)`,
							}}
						/>
					</div>

					{/* Header Content - Right-Aligned Split */}
					<div className='relative grid grid-cols-1 lg:grid-cols-12 gap-8 lg:gap-12'>
						{/* Left Column - Description */}
						<div className='lg:col-span-5 flex items-center order-2 lg:order-1'>
							<div
								className='p-6 lg:p-8 rounded-2xl border backdrop-blur-sm'
								style={{
									background: `linear-gradient(135deg, ${colors.neutral.cloudWhite}90 0%, ${colors.ui.blue50}30 100%)`,
									borderColor: colors.ui.gray200,
									boxShadow: '0 8px 32px rgba(51, 194, 255, 0.08)',
								}}>
								<p
									className='text-lg leading-relaxed font-medium'
									style={{ color: colors.neutral.slateGray }}>
									{LANDING_PAGE_DATA.sectionHeaders.aiDemo.description}
								</p>
							</div>
						</div>

						{/* Right Column - Badge and Title */}
						<div className='lg:col-span-7 space-y-6 text-right order-1 lg:order-2'>
							<div className='flex justify-end'>
								<div
									className='inline-flex items-center space-x-2 rounded-full px-4 sm:px-6 py-2 sm:py-3 border backdrop-blur-sm'
									style={{
										background: `linear-gradient(135deg, ${colors.ui.blue50}80 0%, ${colors.ui.green50}80 100%)`,
										borderColor: colors.ui.gray200,
										boxShadow: '0 4px 16px rgba(128, 237, 153, 0.08)',
									}}>
									<FiMessageCircle
										className='w-4 h-4 sm:w-5 sm:h-5'
										style={{ color: colors.brand.green }}
									/>
									<span
										className='text-xs sm:text-sm font-medium'
										style={{ color: colors.neutral.textBlack }}>
										{LANDING_PAGE_DATA.sectionHeaders.aiDemo.badge}
									</span>
								</div>
							</div>

							<h2 className='text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight'>
								<span style={{ color: colors.brand.navy }}>
									{LANDING_PAGE_DATA.sectionHeaders.aiDemo.title}
								</span>
								<br />
								<span
									className='text-transparent bg-clip-text'
									style={{
										backgroundImage: `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.brand.blue} 100%)`,
									}}>
									{LANDING_PAGE_DATA.sectionHeaders.aiDemo.subtitle}
								</span>
							</h2>
						</div>
					</div>
				</div>

				{/* Main Demo Interface */}
				<div className='max-w-6xl mx-auto'>
					{/* Scenario Selector */}
					<div className='flex justify-center mb-6 sm:mb-8'>
						<div className='flex space-x-2 sm:space-x-3'>
							{scenarios.map((_, index) => (
								<div
									key={index}
									className='w-2.5 h-2.5 sm:w-3 sm:h-3 rounded-full transition-all duration-500'
									style={{
										background:
											currentScenarioIndex === index
												? colors.brand.blue
												: colors.ui.gray300,
										transform:
											currentScenarioIndex === index
												? 'scale(1.3)'
												: 'scale(1)',
									}}
								/>
							))}
						</div>
					</div>

					{/* Demo Container */}
					<div className='grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 mb-8 sm:mb-12'>
						{/* Left Side - Chat Interface */}
						<div
							className='p-4 sm:p-6 lg:p-8 rounded-2xl border'
							style={{
								background: `linear-gradient(135deg, ${colors.ui.blue50}20 0%, ${colors.neutral.cloudWhite} 100%)`,
								borderColor: colors.ui.gray200,
								boxShadow: '0 8px 32px rgba(51, 194, 255, 0.08)',
							}}>
							{/* Chat Header */}
							<div
								className='flex items-center space-x-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b'
								style={{ borderColor: colors.ui.gray200 }}>
								<div
									className='w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center'
									style={{ background: colors.brand.blue }}>
									<FiMessageCircle className='w-4 h-4 sm:w-5 sm:h-5 text-white' />
								</div>
								<div>
									<h3
										className='text-sm sm:text-base font-semibold'
										style={{ color: colors.neutral.textBlack }}>
										AI Assistant
									</h3>
									<p
										className='text-sm'
										style={{ color: colors.neutral.slateGray }}>
										{currentScenario.id === 'cozy-cafe'
											? '☕ Finding a Work Cafe'
											: '🍽️ Planning a Romantic Dinner'}
									</p>
								</div>
							</div>

							{/* Chat Messages */}
							<div className='space-y-4 min-h-[300px]'>
								{/* User Message */}
								{showUserMessage && (
									<div className='flex justify-end'>
										<div
											className='max-w-[80%] p-4 rounded-2xl rounded-br-md'
											style={{
												background: colors.brand.blue,
												color: 'white',
											}}>
											<p className='text-sm'>
												{userText}
												<span className='animate-pulse'>|</span>
											</p>
										</div>
									</div>
								)}

								{/* Typing Indicator */}
								{isTyping && (
									<div className='flex justify-start'>
										<div
											className='p-4 rounded-2xl rounded-bl-md'
											style={{
												background: colors.ui.gray100,
											}}>
											<div className='flex space-x-1'>
												<div className='w-2 h-2 rounded-full bg-gray-400 animate-pulse'></div>
												<div
													className='w-2 h-2 rounded-full bg-gray-400 animate-pulse'
													style={{ animationDelay: '0.2s' }}></div>
												<div
													className='w-2 h-2 rounded-full bg-gray-400 animate-pulse'
													style={{ animationDelay: '0.4s' }}></div>
											</div>
										</div>
									</div>
								)}

								{/* AI Response */}
								{showAIResponse && (
									<div className='flex justify-start'>
										<div
											className='max-w-[80%] p-4 rounded-2xl rounded-bl-md'
											style={{
												background: colors.ui.gray100,
											}}>
											<p
												className='text-sm'
												style={{ color: colors.neutral.textBlack }}>
												{aiText}
												<span className='animate-pulse'>|</span>
											</p>
										</div>
									</div>
								)}
							</div>
						</div>

						{/* Right Side - Processing Steps */}
						<div
							className='p-8 rounded-2xl border'
							style={{
								background: `linear-gradient(135deg, ${colors.ui.green50}20 0%, ${colors.neutral.cloudWhite} 100%)`,
								borderColor: colors.ui.gray200,
								boxShadow: '0 8px 32px rgba(128, 237, 153, 0.08)',
							}}>
							{/* Processing Header */}
							<div
								className='flex items-center space-x-3 mb-6 pb-4 border-b'
								style={{ borderColor: colors.ui.gray200 }}>
								<div
									className='w-10 h-10 rounded-full flex items-center justify-center'
									style={{ background: colors.brand.green }}>
									<FiZap className='w-5 h-5 text-white' />
								</div>
								<div>
									<h3
										className='font-semibold'
										style={{ color: colors.neutral.textBlack }}>
										AI Processing
									</h3>
									<p
										className='text-sm'
										style={{ color: colors.neutral.slateGray }}>
										Real-time analysis
									</p>
								</div>
							</div>

							{/* Processing Steps */}
							<div className='space-y-4'>
								{processingSteps.map((step, index) => (
									<div
										key={index}
										className={`flex items-start space-x-3 p-3 rounded-xl transition-all duration-500 ${
											step.isActive ? 'scale-105' : ''
										}`}
										style={{
											background: step.isActive
												? `linear-gradient(135deg, ${colors.brand.green}15 0%, ${colors.brand.blue}15 100%)`
												: 'transparent',
											border: step.isActive
												? `1px solid ${colors.brand.green}30`
												: '1px solid transparent',
										}}>
										<div
											className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-500 ${
												step.isActive ? 'animate-pulse' : ''
											}`}
											style={{
												background: step.isActive
													? colors.brand.green
													: colors.ui.gray200,
												color: step.isActive
													? 'white'
													: colors.neutral.slateGray,
											}}>
											{step.icon}
										</div>
										<div className='flex-1'>
											<h4
												className='font-medium text-sm'
												style={{
													color: step.isActive
														? colors.neutral.textBlack
														: colors.neutral.slateGray,
												}}>
												{step.title}
											</h4>
											<p
												className='text-xs mt-1'
												style={{
													color: step.isActive
														? colors.neutral.slateGray
														: colors.ui.gray400,
												}}>
												{step.description}
											</p>
										</div>
									</div>
								))}
							</div>
						</div>
					</div>

					{/* Results Section */}
					{showResults && (
						<div
							className='p-8 rounded-2xl border animate-fadeIn'
							style={{
								background: `linear-gradient(135deg, ${colors.brand.green}10 0%, ${colors.brand.blue}10 100%)`,
								borderColor: colors.brand.green + '30',
								boxShadow: '0 8px 32px rgba(128, 237, 153, 0.12)',
							}}>
							{/* Results Header */}
							<div className='text-center mb-6'>
								<div className='flex items-center justify-center space-x-2 mb-4'>
									<div
										className='w-8 h-8 rounded-full flex items-center justify-center'
										style={{ background: colors.brand.green }}>
										<FiStar className='w-4 h-4 text-white' />
									</div>
									<h3
										className='text-lg font-semibold'
										style={{ color: colors.neutral.textBlack }}>
										Perfect Matches Found
									</h3>
								</div>
								<p
									className='text-sm'
									style={{ color: colors.neutral.slateGray }}>
									AI found {currentScenario.searchResults.length} locations that
									match your preferences
								</p>
							</div>

							{/* Results Grid */}
							<div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4'>
								{currentScenario.searchResults
									.slice(0, 3)
									.map((result, index) => (
										<div
											key={index}
											className='p-3 sm:p-4 rounded-xl border bg-white/50 hover:bg-white/80 transition-all duration-300'
											style={{
												borderColor: colors.ui.gray200,
												animationDelay: `${index * 200}ms`,
												animation: 'slideInUp 0.6s ease-out forwards',
											}}>
											<div className='flex items-start justify-between mb-2'>
												<h4
													className='font-medium text-xs sm:text-sm'
													style={{ color: colors.neutral.textBlack }}>
													{result.name}
												</h4>
												<div className='flex items-center space-x-1'>
													<FiStar
														className='w-3 h-3'
														style={{ color: colors.brand.green }}
													/>
													<span
														className='text-xs font-medium'
														style={{ color: colors.neutral.textBlack }}>
														{result.rating}
													</span>
												</div>
											</div>
											<p
												className='text-xs mb-2 sm:mb-3'
												style={{ color: colors.neutral.slateGray }}>
												{result.type} • {result.distance}
											</p>
											<div className='flex flex-wrap gap-1'>
												{result.features
													.slice(0, 3)
													.map((feature, featureIndex) => (
														<span
															key={featureIndex}
															className='px-2 py-1 rounded-full text-xs'
															style={{
																background: colors.ui.blue50,
																color: colors.brand.blue,
															}}>
															{feature}
														</span>
													))}
											</div>
										</div>
									))}
							</div>
						</div>
					)}

					{/* Call to Action */}
					<div className='text-center mt-12'>
						<button
							onClick={onGetStarted}
							className='inline-flex items-center space-x-2 px-8 py-4 rounded-2xl font-semibold text-white transition-all duration-300 hover:scale-105 hover:shadow-lg'
							style={{
								background: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
								boxShadow: '0 4px 20px rgba(51, 194, 255, 0.3)',
							}}>
							<span>Try AI Search Now</span>
							<FiArrowRight className='w-5 h-5' />
						</button>
						<p
							className='text-sm mt-4'
							style={{ color: colors.neutral.slateGray }}>
							Experience the power of AI-driven location discovery
						</p>
					</div>
				</div>
			</div>
		</div>
	);
};

export default AIShowcase;
