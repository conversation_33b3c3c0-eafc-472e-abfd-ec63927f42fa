/** @format */

'use client';

import { colors } from '@/app/colors';
import { useViewportHeight } from '@/app/landing/utils/responsiveUtils';
import { LANDING_PAGE_DATA } from '@/app/shared/poi/constants';
import React from 'react';
import { FiMapPin } from 'react-icons/fi';
import DynamicCategoryMosaic from './DynamicCategoryMosaic';

interface CategoryExplorerProps {
	onCategorySelect?: (category: any) => void;
}

const CategoryExplorer: React.FC<CategoryExplorerProps> = ({
	onCategorySelect,
}) => {
	// Use viewport height for responsive section sizing
	const { screenSize, minHeight, padding } = useViewportHeight('section');

	return (
		<div
			className='bg-transparent viewport-section'
			style={{
				minHeight,
				padding: `${padding} 0`,
			}}>
			{/* Asymmetric Container Layout */}
			<div className='w-full px-4 sm:px-6 lg:px-8'>
				{/* Floating Header Design */}
				<div className='relative mb-16 lg:mb-20'>
					{/* Background accent shapes */}
					<div className='absolute inset-0 opacity-10'>
						<div
							className='absolute top-0 left-0 w-32 h-32 rounded-full'
							style={{
								background: `radial-gradient(circle, ${colors.brand.blue}40 0%, transparent 70%)`,
							}}
						/>
						<div
							className='absolute top-8 right-1/4 w-20 h-20 rounded-lg rotate-45'
							style={{
								background: `linear-gradient(45deg, ${colors.brand.green}30 0%, transparent 70%)`,
							}}
						/>
					</div>

					{/* Header Content - Asymmetric Layout */}
					<div className='relative grid grid-cols-1 lg:grid-cols-12 gap-8 lg:gap-12'>
						{/* Left Column - Badge and Title */}
						<div className='lg:col-span-7 space-y-6'>
							<div
								className='inline-flex items-center space-x-2 rounded-full px-6 py-3 border backdrop-blur-sm'
								style={{
									background: `linear-gradient(135deg, ${colors.ui.green50}80 0%, ${colors.ui.blue50}80 100%)`,
									borderColor: colors.ui.gray200,
									boxShadow: '0 4px 16px rgba(51, 194, 255, 0.08)',
								}}>
								<FiMapPin
									className='w-5 h-5'
									style={{ color: colors.brand.blue }}
								/>
								<span
									className='text-sm font-medium'
									style={{ color: colors.neutral.textBlack }}>
									{LANDING_PAGE_DATA.sectionHeaders.categoryExplorer.badge}
								</span>
							</div>

							<h2 className='text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight'>
								<span style={{ color: colors.brand.navy }}>
									{LANDING_PAGE_DATA.sectionHeaders.categoryExplorer.title}
								</span>
								<br />
								<span
									className='text-transparent bg-clip-text'
									style={{
										backgroundImage: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
									}}>
									{LANDING_PAGE_DATA.sectionHeaders.categoryExplorer.subtitle}
								</span>
							</h2>
						</div>

						{/* Right Column - Description */}
						<div className='lg:col-span-5 flex items-center'>
							<div
								className='p-6 lg:p-8 rounded-2xl border backdrop-blur-sm'
								style={{
									background: `linear-gradient(135deg, ${colors.neutral.cloudWhite}90 0%, ${colors.ui.green50}30 100%)`,
									borderColor: colors.ui.gray200,
									boxShadow: '0 8px 32px rgba(128, 237, 153, 0.08)',
								}}>
								<p
									className='text-lg leading-relaxed font-medium'
									style={{ color: colors.neutral.slateGray }}>
									{
										LANDING_PAGE_DATA.sectionHeaders.categoryExplorer
											.description
									}
								</p>
							</div>
						</div>
					</div>
				</div>

				{/* Category Mosaic - Using the old design */}
				<DynamicCategoryMosaic onCategorySelect={onCategorySelect} />
			</div>
		</div>
	);
};

export default CategoryExplorer;
