/** @format */

import { useEffect, useState } from 'react';

// Three-level responsive system for 320px, 768px, and 1024px breakpoints
export const BREAKPOINTS = {
	mobile: 320,
	tablet: 768,
	desktop: 1024,
} as const;

// Responsive scaling utilities
export const getResponsiveValue = (
	mobile: number | string,
	tablet: number | string,
	desktop: number | string
) => ({
	mobile,
	tablet,
	desktop,
});

// Typography scaling system
export const TYPOGRAPHY_SCALE = {
	// Hero section typography
	heroTitle: getResponsiveValue('2rem', '3rem', '4rem'), // 32px, 48px, 64px
	heroSubtitle: getResponsiveValue('1rem', '1.25rem', '1.5rem'), // 16px, 20px, 24px
	heroDescription: getResponsiveValue('0.875rem', '1rem', '1.125rem'), // 14px, 16px, 18px

	// Section headers
	sectionTitle: getResponsiveValue('1.75rem', '2.5rem', '3rem'), // 28px, 40px, 48px
	sectionSubtitle: getResponsiveValue('0.875rem', '1rem', '1.125rem'), // 14px, 16px, 18px

	// UI elements
	buttonText: getResponsiveValue('0.75rem', '0.875rem', '1rem'), // 12px, 14px, 16px
	cardTitle: getResponsiveValue('0.875rem', '1rem', '1.125rem'), // 14px, 16px, 18px
	cardText: getResponsiveValue('0.75rem', '0.875rem', '1rem'), // 12px, 14px, 16px
};

// Spacing system
export const SPACING_SCALE = {
	// Container padding
	containerPadding: getResponsiveValue('1rem', '1.5rem', '2rem'), // 16px, 24px, 32px

	// Section spacing
	sectionPadding: getResponsiveValue('2rem', '3rem', '4rem'), // 32px, 48px, 64px
	elementGap: getResponsiveValue('0.75rem', '1rem', '1.5rem'), // 12px, 16px, 24px

	// Component spacing
	cardPadding: getResponsiveValue('0.75rem', '1rem', '1.25rem'), // 12px, 16px, 20px
	buttonPadding: getResponsiveValue(
		'0.5rem 0.75rem',
		'0.75rem 1rem',
		'1rem 1.25rem'
	),
};

// Component sizing
export const COMPONENT_SIZES = {
	// Hero preview cards
	heroCardWidth: getResponsiveValue('280px', '320px', '380px'),
	heroCardMaxWidth: getResponsiveValue('90vw', '45vw', '380px'),

	// Filter buttons
	filterButtonPadding: getResponsiveValue(
		'0.5rem 0.75rem',
		'0.75rem 1rem',
		'1rem 1.5rem'
	),
	filterButtonRadius: getResponsiveValue('0.75rem', '1rem', '1.25rem'),

	// Category mosaic
	mosaicHeight: getResponsiveValue('300px', '500px', '600px'),
	mosaicMaxHeight: getResponsiveValue('40vh', '50vh', '60vh'),
};

// Dynamic height calculation utilities
export const calculateDynamicHeight = (
	baseHeight: string,
	contentHeight: number,
	minHeight: string,
	maxHeight: string
) => {
	return {
		height: `max(${minHeight}, min(${maxHeight}, ${baseHeight}))`,
		minHeight,
		maxHeight,
	};
};

// Viewport-aware height calculation
export const calculateViewportHeight = (
	sectionType: 'hero' | 'section',
	screenSize: 'mobile' | 'tablet' | 'desktop'
) => {
	const config = VIEWPORT_HEIGHT_CONFIG[sectionType];
	return {
		height: config.heights[screenSize],
		minHeight: config.minHeights[screenSize],
		maxHeight: config.maxHeights[screenSize],
		padding: config.contentSpacing[screenSize],
	};
};

// Hook for dynamic viewport sizing
export const useViewportHeight = (
	sectionType: 'hero' | 'section' = 'section'
) => {
	const [screenSize, setScreenSize] = useState<'mobile' | 'tablet' | 'desktop'>(
		'desktop'
	);
	const [viewportHeight, setViewportHeight] = useState(0);

	useEffect(() => {
		const updateSize = () => {
			const width = window.innerWidth;
			const height = window.innerHeight;

			setViewportHeight(height);

			if (width < 768) {
				setScreenSize('mobile');
			} else if (width < 1024) {
				setScreenSize('tablet');
			} else {
				setScreenSize('desktop');
			}
		};

		updateSize();
		window.addEventListener('resize', updateSize);
		return () => window.removeEventListener('resize', updateSize);
	}, []);

	const heightConfig = calculateViewportHeight(sectionType, screenSize);

	return {
		screenSize,
		viewportHeight,
		heightConfig,
		...heightConfig,
	};
};

// Responsive class generator
export const generateResponsiveClasses = (
	mobileClasses: string,
	tabletClasses: string,
	desktopClasses: string
) => {
	return `${mobileClasses} md:${tabletClasses} lg:${desktopClasses}`;
};

// CSS custom properties for responsive values
export const generateCSSVariables = () => {
	return {
		'--mobile-breakpoint': `${BREAKPOINTS.mobile}px`,
		'--tablet-breakpoint': `${BREAKPOINTS.tablet}px`,
		'--desktop-breakpoint': `${BREAKPOINTS.desktop}px`,

		// Typography
		'--hero-title-mobile': TYPOGRAPHY_SCALE.heroTitle.mobile,
		'--hero-title-tablet': TYPOGRAPHY_SCALE.heroTitle.tablet,
		'--hero-title-desktop': TYPOGRAPHY_SCALE.heroTitle.desktop,

		// Spacing
		'--container-padding-mobile': SPACING_SCALE.containerPadding.mobile,
		'--container-padding-tablet': SPACING_SCALE.containerPadding.tablet,
		'--container-padding-desktop': SPACING_SCALE.containerPadding.desktop,

		// Component sizes
		'--hero-card-width-mobile': COMPONENT_SIZES.heroCardWidth.mobile,
		'--hero-card-width-tablet': COMPONENT_SIZES.heroCardWidth.tablet,
		'--hero-card-width-desktop': COMPONENT_SIZES.heroCardWidth.desktop,
	};
};

// Responsive style generator for inline styles
export const getResponsiveStyles = (
	property: string,
	values: { mobile: string; tablet: string; desktop: string }
) => {
	return {
		[property]: values.mobile,
		[`@media (min-width: ${BREAKPOINTS.tablet}px)`]: {
			[property]: values.tablet,
		},
		[`@media (min-width: ${BREAKPOINTS.desktop}px)`]: {
			[property]: values.desktop,
		},
	};
};

// Dynamic layout configuration for zig-zag design
export const DYNAMIC_LAYOUT_CONFIG = {
	// Width constraints for sections
	sectionWidths: {
		mobile: {
			min: '90%',
			max: '95%',
			preferred: '92%',
		},
		tablet: {
			min: '70%',
			max: '85%',
			preferred: '80%',
		},
		desktop: {
			min: '60%',
			max: '75%',
			preferred: '70%',
		},
	},

	// Positioning for zig-zag layout
	positioning: {
		hero: 'center' as const, // Hero stays centered for brand focus
		section1: 'left' as const, // CategoryExplorer - left aligned
		section2: 'right' as const, // AIShowcase - right aligned
		footer: 'center' as const, // Footer centered
	},

	// Margins and spacing for dynamic layout
	sectionSpacing: {
		mobile: {
			horizontal: '5%',
			vertical: '3rem',
		},
		tablet: {
			horizontal: '10%',
			vertical: '4rem',
		},
		desktop: {
			horizontal: '15%',
			vertical: '5rem',
		},
	},
};

// Viewport-based height utilities
export const VIEWPORT_HEIGHT_CONFIG = {
	// Hero section - 95% viewport height for better visual spacing
	hero: {
		heights: {
			mobile: '95vh',
			tablet: '95vh',
			desktop: '95vh',
		},
		minHeights: {
			mobile: '95vh',
			tablet: '95vh',
			desktop: '95vh',
		},
		maxHeights: {
			mobile: '95vh',
			tablet: '95vh',
			desktop: '95vh',
		},
		contentSpacing: {
			mobile: '0.5rem',
			tablet: '1rem',
			desktop: '1.5rem',
		},
	},
	// Other sections - Responsive to content but with viewport awareness
	section: {
		heights: {
			mobile: 'auto',
			tablet: 'auto',
			desktop: 'auto',
		},
		minHeights: {
			mobile: '50vh',
			tablet: '60vh',
			desktop: '70vh',
		},
		maxHeights: {
			mobile: 'none',
			tablet: 'none',
			desktop: 'none',
		},
		contentSpacing: {
			mobile: '2rem',
			tablet: '3rem',
			desktop: '4rem',
		},
	},
};

// Legacy support - keeping for backward compatibility
export const HERO_RESPONSIVE_CONFIG = VIEWPORT_HEIGHT_CONFIG.hero;

// CSS utility for cross-browser viewport height support
export const getViewportHeightCSS = (height: string) => {
	// Support for older browsers and mobile Safari
	return {
		height: height,
		minHeight: height,
		// Fallback for browsers that don't support vh units properly
		minHeightFallback: height === '100vh' ? '100vh' : height,
		// Support for mobile browsers with dynamic viewport
		...(height === '100vh' && {
			minHeight: '100vh',
			minHeightWebkit: '-webkit-fill-available',
		}),
	};
};

// Filter button responsive configuration
export const FILTER_RESPONSIVE_CONFIG = {
	fontSize: getResponsiveValue('0.75rem', '0.875rem', '1rem'),
	padding: getResponsiveValue('0.5rem 0.75rem', '0.75rem 1rem', '1rem 1.5rem'),
	borderRadius: getResponsiveValue('0.75rem', '1rem', '1.25rem'),
	gap: getResponsiveValue('0.5rem', '0.75rem', '1rem'),
};

// Content scaling utilities
export const scaleContent = (
	baseSize: number,
	scale: 'mobile' | 'tablet' | 'desktop'
) => {
	const scales = {
		mobile: 0.8,
		tablet: 0.9,
		desktop: 1,
	};
	return baseSize * scales[scale];
};
